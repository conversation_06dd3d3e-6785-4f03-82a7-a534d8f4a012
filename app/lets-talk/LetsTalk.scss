.get-in-touch-section {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 50px;

  .get-in-touch-header {
    background: #093246;
    width: 100%;
    height: 500px;
    display: flex;
    align-items: center;
    justify-content: center;

    .get-in-touch-title {
      font-family: Ruluko !important;
      font-weight: 400;
      font-size: 80px;
      line-height: 30px;
      letter-spacing: 0%;
      color: #ffffff;
      vertical-align: middle;

      margin-top: -250px;

      @media (max-width: 768px) {
        font-size: 60px;
      }

      @media (max-width: 480px) {
        font-size: 40px;
      }
    }
  }

  .contact-form-card {
    background-color: #d9d9d9;
    border-radius: 20px;
    padding: 40px;
    width: 100%;
    max-width: 1130px;
    margin-top: -170px;
    margin-bottom: 185px;
    position: relative;
    z-index: 1;

    .contact-us-title {
      font-family: Poppins;
      font-weight: 400;
      font-size: 40px;
      line-height: 30px;
      letter-spacing: 0%;
      background: #000000;
      background-clip: text;
      -webkit-text-fill-color: transparent;
      text-align: left;
      margin-bottom: 30px;
      margin-left: 20px;

      @media (max-width: 768px) {
        font-size: 32px;
      }

      @media (max-width: 480px) {
        font-size: 28px;
      }
    }

    .category-buttons {
      display: flex;
      gap: 8px;
      margin-bottom: 50px;
      padding-bottom: 10px;

      .category-button {
        flex: 1;
        background-color: #ffffff;
        border: 1px solid #ffffff;
        padding: 9px 0;
        font-family: Poppins;
        font-size: 20px;
        border-radius: 10px;
        box-shadow: 4px 4px 4px 0px #00000040;
        font-weight: 400;
        color: #333;
        cursor: pointer;
        transition: all 0.3s ease;
        white-space: nowrap;
        text-align: center;

        &.active {
          background-color: #d77d46;
          color: #000000;
          border-color: #d77d46;
        }

        @media (max-width: 480px) {
          font-size: 12px;
          padding: 8px 0;
        }
      }
    }
    .divider {
      border: 1px solid #000000;
    }
    .contact-form {
      margin-top: 31px;
      .form-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 20px;

        gap: 20px;

        @media (max-width: 768px) {
          flex-direction: column;
          gap: 15px;
        }

        .form-group {
          flex: 1;
          display: flex;
          flex-direction: column;

          label {
            font-family: Poppins !important;
            font-weight: 300;
            font-size: 20px;
            line-height: 30px;
            letter-spacing: 0%;
            text-align: justify;
            vertical-align: middle;
            color: #000000;
            margin-bottom: 5px;
          }

          input[type="text"],
          input[type="email"],
          input[type="tel"] {
            background-color: #e9e9e9;
            border: 1px solid #8c8a8a;
            // border-radius: 6px;
            padding: 12px 15px;
            font-family: Poppins;
            font-size: 14px;
            color: #333;
            width: 100%;
            box-sizing: border-box;
            transition: all 0.3s ease;

            &:focus {
              outline: none;
              border-color: #d77d46;
              background-color: #ffffff;
              box-shadow: 0 0 0 2px rgba(215, 125, 70, 0.1);
            }

            &::placeholder {
              color: #999;
            }
          }
        }
      }

      .form-group-message-group {
        margin-bottom: 20px;

        label {
          font-family: Poppins !important;
          font-weight: 300;
          font-size: 20px;
          line-height: 30px;
          letter-spacing: 0%;
          text-align: justify;
          vertical-align: middle;
          color: #000000;
          margin-bottom: 5px;
        }

        textarea {
          background-color: #e9e9e9;
          border: 1px solid #8c8a8a;
          padding: 15px;
          font-family: Poppins;
          font-size: 14px;
          color: #333;
          width: 100%;
          box-sizing: border-box;
          resize: vertical;
          min-height: 286px;
          transition: all 0.3s ease;
          margin-top: 5px;

          &:focus {
            outline: none;
            border-color: #d77d46;
            background-color: #ffffff;
            box-shadow: 0 0 0 2px rgba(215, 125, 70, 0.1);
          }

          &::placeholder {
            color: #999;
          }
        }

        .char-count {
          display: block;
          text-align: right;
          font-size: 12px;
          color: #666;
          margin-top: 5px;
        }
      }

      .checkbox-group {
        display: flex;
        align-items: flex-start;
        margin-bottom: 20px;
        gap: 24px;

        input[type="checkbox"] {
          margin-top: 2px;
          width: 30px;
          height: 35px;
          cursor: pointer;
          accent-color: #d77d46;
        }

        label {
          font-family: Poppins;
          font-size: 20px;
          color: #000000;
          // line-height: 1.5;
          flex: 1;

          a {
            color: #2499e2;
            text-decoration: none;

            &:hover {
              text-decoration: underline;
            }
          }
        }
      }

      .submit-message-button {
        background-color: #d77d46;
        color: #ffffff;
        border: none;
        border-radius: 30px;
        padding: 15px 80px 17px 80px;
        font-family: Poppins;
        font-size: 20px;
        font-weight: 500;
        cursor: pointer;
        display: block;
        margin: 80px auto 0 auto;
        transition: all 0.3s ease;
        min-width: 120px;

        &:hover {
          background-color: #c26b3d;
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(215, 125, 70, 0.3);
        }

        @media (max-width: 480px) {
          font-size: 14px;
          padding: 10px 32px;
        }
      }
    }
  }

  .contact-info-section {
    background: #09324680;
    width: 100%;
    padding: 80px 0;
    display: flex;
    justify-content: center;
    gap: 200px;
    margin-top: 50px;

    @media (max-width: 1024px) {
      gap: 100px;
      padding: 60px 40px;
    }

    @media (max-width: 768px) {
      flex-direction: column;
      gap: 60px;
      padding: 40px 20px;
      align-items: center;
    }

    .contact-info-card {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      color: #ffffff;
      font-family: Poppins;
      text-align: left;
      // max-width: 300px;

      @media (max-width: 768px) {
        align-items: center;
        text-align: center;
        max-width: 100%;
      }

      .location-header {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        @media (max-width: 768px) {
          justify-content: center;
        }

        .icon-placeholder {
          font-size: 28px;
          color: #46aed7;
          margin-right: 15px;

          @media (max-width: 768px) {
            margin-right: 10px;
          }
        }

        h3 {
          font-family: Poppins;
          font-weight: 400;
          font-size: 40px;
          margin: 0;
          color: #ffffff;
          letter-spacing: 0.5px;

          @media (max-width: 768px) {
            font-size: 28px;
          }
        }
      }
      .email-container {
        display: flex;
        align-items: center;
        margin-bottom: 20px;

        @media (max-width: 768px) {
          justify-content: center;
        }

        .icon-placeholder {
          font-size: 28px;

          margin-right: 15px;

          @media (max-width: 768px) {
            margin-right: 10px;
          }
        }

        a {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;
          color: #ffffff;
          text-decoration: underline;
        }
      }

      .address-line {
        font-family: Poppins !important;
        font-weight: 400;
        font-size: 20px;
        line-height: 36px;
        letter-spacing: 0%;
        text-align: justify;
        vertical-align: middle;
        color: #ffffff;
      }

      .phone-number {
        display: flex;
        align-items: center;
        margin-top: 20px;
        margin-bottom: 15px;

        @media (max-width: 768px) {
          justify-content: center;
        }

        .icon-placeholder {
          font-size: 22px;
          color: #46aed7;
          margin-right: 12px;
        }

        .phone-number-text {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;
          line-height: 36px;
          letter-spacing: 0%;
          text-align: justify;
          vertical-align: middle;
        }

        span {
          font-family: Poppins;
          font-size: 18px;
          color: #b8c5d1;
          font-weight: 300;

          @media (max-width: 768px) {
            font-size: 16px;
          }
        }
      }

      a {
        color: #46aed7;
        text-decoration: none;
        font-size: 18px;
        font-family: Poppins;
        font-weight: 400;
        transition: color 0.3s ease;

        &:hover {
          color: #5bc0e8;
          text-decoration: underline;
        }

        @media (max-width: 768px) {
          font-size: 16px;
        }
      }
    }
  }
}
