.ar-vr-page {
  .ar-vr-content {
    padding-bottom: 150px;
    .ar-vr-image {
      display: flex;
      justify-content: center;
      align-items: center;

      img {
        width: 100%;
        min-width: 1231px;
        min-height: 674px;
        border-radius: 30px;
      }
    }

    .ar-vr-text {
      margin-top: 80px;
      padding: 0 70px;
      margin-bottom: 80px;

      .ar-vr-title {
        font-family: Poppins;
        font-weight: 400;
        font-size: 36px;

        line-height: 30px;
        letter-spacing: 0%;
        color: #8cffe4;
        margin-bottom: 50px;
      }

      .ar-vr-description {
        font-family: Poppins;
        font-weight: 400;
        font-size: 20px;
        line-height: 30px;
        letter-spacing: 0%;
        text-align: justify;
        color: #ffffff;
      }
    }

    .future-vision {
      padding: 0 70px;
      margin-bottom: 50px;

      .future-vision-title {
        font-family: Poppins;
        font-weight: 300;
        font-size: 34px;

        letter-spacing: 0%;
        vertical-align: middle;
        background: linear-gradient(
          180deg,
          #8cffe4 37.36%,
          #549989 100%,
          #549989 100%
        );
        background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 50px;
      }

      .future-vision-description {
        font-family: Poppins;
        font-weight: 400;
        font-size: 20px;

        line-height: 30px;
        letter-spacing: 0%;
        text-align: justify;
        vertical-align: middle;
        color: #ffffff;
        margin-bottom: 40px;
      }

      .future-vision-description1 {
        font-family: Poppins;
        font-weight: 400;
        font-size: 20px;
        line-height: 30px;
        letter-spacing: 0%;
        text-align: justify;
        vertical-align: middle;
        color: #ffffff;
        margin-bottom: 40px;
      }

      .future-vision-bullets {
        .future-vision-bullets-description {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;
          line-height: 30px;
          letter-spacing: 0%;
          text-align: justify;
          vertical-align: middle;
          color: #ffffff;
        }

        .future-vision-bullets-list {
          margin-bottom: 50px;

          .future-vision-bullets-item {
            position: relative;
            padding-left: 20px;
            display: flex;
            align-items: start;

            &::before {
              content: "•";
              position: absolute;
              left: 0;
              color: #ffffff;
              font-size: 20px;
            }

            .future-vision-bullets-title {
              font-family: Poppins;
              font-weight: 600;
              font-size: 20px;
              width: 100%;
              line-height: 30px;
              letter-spacing: 0%;
              // text-align: justify;
              // vertical-align: middle;
              color: #ffffff;
            }

            .future-vision-bullets-text {
              font-family: Poppins;
              font-weight: 400;
              font-size: 20px;
              line-height: 30px;
              letter-spacing: 0%;
              text-align: justify;
              // vertical-align: middle;
              color: #ffffff;
            }
          }
        }

        .future-vision-conclusion {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;

          line-height: 30px;
          letter-spacing: 0%;
          text-align: justify;
          color: #ffffff;
        }
      }
    }

    .what-we-offer {
      padding: 0 70px;
      margin-bottom: 80px;

      .what-we-offer-text {
        padding-top: 20px;
        margin-bottom: 30px;
        font-family: Poppins;
        font-weight: 400;
        font-size: 20px;

        line-height: 30px;
        letter-spacing: 0%;
        text-align: justify;
        vertical-align: middle;
        color: #ffffff;
      }

      .what-we-offer-title {
        font-family: Poppins;
        font-weight: 300;
        font-size: 34px;
        letter-spacing: 0%;
        vertical-align: middle;
        background: linear-gradient(
          180deg,
          #8cffe4 37.36%,
          #549989 100%,
          #549989 100%
        );

        background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 30px;
      }

      .what-we-offer-bullets {
        margin-bottom: 40px;

        .what-we-offer-item {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;

          line-height: 30px;
          letter-spacing: 0%;
          text-align: justify;
          vertical-align: middle;
          color: #ffffff;

          &::before {
            content: "•";
            position: absolute;
            left: 0;
            color: #ffffff;
            font-size: 20px;
          }
        }

        .what-we-offer-item-text {
          font-family: Poppins;
          font-weight: 400;
          font-size: 20px;

          line-height: 30px;
          letter-spacing: 0%;
          text-align: justify;
          vertical-align: middle;
          color: #ffffff;
        }
      }

      .what-we-offer-conclusion {
        font-family: Poppins;
        font-weight: 400;
        font-size: 20px;

        line-height: 30px;
        letter-spacing: 0%;
        text-align: justify;
        vertical-align: middle;
        color: #ffffff;
      }
    }

    .Creative-Design {
      padding: 0 70px;
      .Creative-Design-title {
        font-family: Poppins;
        font-weight: 300;
        font-size: 34px;

        letter-spacing: 0%;
        vertical-align: middle;
        background: linear-gradient(
          180deg,
          #8cffe4 37.36%,
          #549989 100%,
          #549989 100%
        );
        background-clip: text;
        background-clip: text;
        -webkit-text-fill-color: transparent;
        margin-bottom: 50px;
      }

      .Creative-Design-text {
        font-family: Poppins;
        font-weight: 400;
        font-size: 20px;

        line-height: 30px;
        letter-spacing: 0%;
        text-align: justify;
        vertical-align: middle;
        color: #ffffff;
        margin-bottom: 40px;
      }

      .Creative-Design-bullets-and-image {
        display: flex;
        justify-content: space-between;

        .Creative-Design-bullets {
          .Creative-Design-bullets-title {
            font-family: Poppins;
            font-weight: 400;
            font-size: 20px;
            line-height: 30px;
            letter-spacing: 0%;
            vertical-align: middle;
            color: #FFFFFF;
            margin-bottom: 20px;
          }

          .Creative-Design-bullets-list {
            .Creative-Design-bullets-item {
              .Creative-Design-bullets-text {
                font-family: Poppins;
                font-weight: 400;
                font-size: 20px;

                line-height: 27px;
                letter-spacing: 0%;
                vertical-align: middle;
                color: #ffffff;
              }

              &::before {
                content: "•";
                position: absolute;
                left: 0;
                color: #ffffff;
                font-size: 20px;
              }
            }
          }
        }

        .Creative-Design-image {
          display: flex;
          flex-direction: column;
          align-items: center;
          gap: 40px;

          .Creative-Design-image-caption {
            font-family: Poppins;
            font-weight: 275 !important;
            font-size: 24px;

            letter-spacing: 0%;
            text-align: center;
            color: #ffffff;
          }
        }
      }
    }
  }
}

.metaverse-centre-btn.blink {
  animation: blink-blue 1s steps(2, start) infinite;
}

.metaverse-centre-text.blink {
  animation: blink-blue 1s steps(2, start) infinite;
}

@keyframes blink-blue {
  0%,
  100% {
    background: #7be0c9;
    color: #fff;
  }
  50% {
    background: #7be0c9;
    color: #fff;
  }
}
