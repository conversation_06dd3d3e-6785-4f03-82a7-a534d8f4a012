import React from "react";
import { Dialog, DialogContent, IconButton, Box } from "@mui/material";
import CloseIcon from "@mui/icons-material/Close";
import Image, { StaticImageData } from "next/image";

interface AR_VR_PopUpProps {
  open: boolean;
  onClose: () => void;
  image: StaticImageData | string;
  alt?: string;
}

const AR_VR_PopUp: React.FC<AR_VR_PopUpProps> = ({
  open,
  onClose,
  image,
  alt = "AR VR Image",
}) => {
  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="lg"
      fullWidth
      PaperProps={{
        sx: {
          backgroundColor: "transparent",
          boxShadow: "none",
          overflow: "hidden",
          maxWidth: { xs: "90vw", sm: "80vw", md: "70vw" },
          margin: { xs: "16px", sm: "24px" },
          borderRadius: "8px",
        },
      }}
      BackdropProps={{
        sx: { backgroundColor: "rgba(0, 0, 0, 0.6)" },
      }}
    >
      <DialogContent
        sx={{
          padding: { xs: "16px", sm: "24px" },
          position: "relative",
          overflow: "hidden",
          display: "flex",
          flexDirection: "column",
          alignItems: "flex-end",
        }}
      >
        <IconButton
          onClick={onClose}
          aria-label="Close dialog"
          sx={{
            position: "absolute",
            top: { xs: "25px", sm: "70px" },
            right: { xs: "8px", sm: "60px" },
            color: "#fff",
            backgroundColor: "rgba(0, 0, 0, 0.5)",
            "&:hover": {
              backgroundColor: "rgba(0, 0, 0, 0.7)",
            },
            zIndex: 2,
            width: "30px",
            height: "30px",
            padding: "4px",
            "& .MuiSvgIcon-root": {
              fontSize: "18px",
            },
          }}
        >
          <CloseIcon />
        </IconButton>

        <Box
          sx={{
            position: "relative",
            width: "100%",
            height: { xs: "50vh", sm: "60vh", md: "70vh" },
            maxHeight: "80vh",
            overflow: "hidden",
            marginTop: { xs: "32px", sm: "40px" },
          }}
        >
          <Image
            src={image}
            alt={alt}
            fill
            style={{
              objectFit: "contain",
            }}
            quality={100}
          />
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default AR_VR_PopUp;

